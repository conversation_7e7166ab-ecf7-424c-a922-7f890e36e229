import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:selfeng/features/main_lesson/presentation/widgets/sound_wave_audio_player.dart';

void main() {
  group('SoundWaveAudioPlayer', () {
    testWidgets('should render correctly with initial state', (WidgetTester tester) async {
      // Arrange
      const testAudioUrl = 'https://example.com/test-audio.mp3';
      
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SoundWaveAudioPlayer(
              audioUrl: testAudioUrl,
              isEnabled: true,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(SoundWaveAudioPlayer), findsOneWidget);
      expect(find.byIcon(Icons.play_arrow), findsOneWidget);
      expect(find.text('00:00'), findsOneWidget);
    });

    testWidgets('should show disabled state when isEnabled is false', (WidgetTester tester) async {
      // Arrange
      const testAudioUrl = 'https://example.com/test-audio.mp3';
      
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SoundWaveAudioPlayer(
              audioUrl: testAudioUrl,
              isEnabled: false,
            ),
          ),
        ),
      );

      // Assert
      expect(find.byType(SoundWaveAudioPlayer), findsOneWidget);
      expect(find.byIcon(Icons.play_arrow), findsOneWidget);
    });

    testWidgets('should have proper container styling', (WidgetTester tester) async {
      // Arrange
      const testAudioUrl = 'https://example.com/test-audio.mp3';
      
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SoundWaveAudioPlayer(
              audioUrl: testAudioUrl,
              isEnabled: true,
            ),
          ),
        ),
      );

      // Assert
      final container = tester.widget<Container>(
        find.descendant(
          of: find.byType(SoundWaveAudioPlayer),
          matching: find.byType(Container),
        ).first,
      );
      
      expect(container.decoration, isA<BoxDecoration>());
      final decoration = container.decoration as BoxDecoration;
      expect(decoration.color, equals(Colors.white));
      expect(decoration.borderRadius, isA<BorderRadius>());
    });

    testWidgets('should display play button with correct styling', (WidgetTester tester) async {
      // Arrange
      const testAudioUrl = 'https://example.com/test-audio.mp3';
      
      // Act
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: SoundWaveAudioPlayer(
              audioUrl: testAudioUrl,
              isEnabled: true,
            ),
          ),
        ),
      );

      // Assert
      final playButton = find.byIcon(Icons.play_arrow);
      expect(playButton, findsOneWidget);
      
      final iconWidget = tester.widget<Icon>(playButton);
      expect(iconWidget.color, equals(Colors.white));
      expect(iconWidget.size, equals(24));
    });
  });
}
