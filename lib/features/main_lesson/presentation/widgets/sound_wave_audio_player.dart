import 'dart:async';
import 'dart:math' as math;
import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';

class SoundWaveAudioPlayer extends StatefulWidget {
  final String audioUrl;
  final bool isEnabled;

  const SoundWaveAudioPlayer({
    super.key,
    required this.audioUrl,
    this.isEnabled = true,
  });

  @override
  State<SoundWaveAudioPlayer> createState() => _SoundWaveAudioPlayerState();
}

class _SoundWaveAudioPlayerState extends State<SoundWaveAudioPlayer>
    with TickerProviderStateMixin {
  late AudioPlayer _audioPlayer;
  late AnimationController _waveAnimationController;
  late AnimationController _playButtonController;

  PlayerState _playerState = PlayerState.stopped;
  Duration? _duration;
  Duration _position = Duration.zero;

  StreamSubscription<PlayerState>? _playerStateSubscription;
  StreamSubscription<Duration>? _durationSubscription;
  StreamSubscription<Duration>? _positionSubscription;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();

    // Animation controllers
    _waveAnimationController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _playButtonController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _initializeAudioPlayer();
  }

  void _initializeAudioPlayer() {
    // Listen to player state changes
    _playerStateSubscription = _audioPlayer.onPlayerStateChanged.listen((
      state,
    ) {
      if (mounted) {
        setState(() {
          _playerState = state;
        });

        if (state == PlayerState.playing) {
          _waveAnimationController.repeat();
          _playButtonController.forward();
        } else {
          _waveAnimationController.stop();
          _playButtonController.reverse();
        }
      }
    });

    // Listen to duration changes
    _durationSubscription = _audioPlayer.onDurationChanged.listen((duration) {
      if (mounted) {
        setState(() {
          _duration = duration;
        });
      }
    });

    // Listen to position changes
    _positionSubscription = _audioPlayer.onPositionChanged.listen((position) {
      if (mounted) {
        setState(() {
          _position = position;
        });
      }
    });
  }

  @override
  void dispose() {
    _playerStateSubscription?.cancel();
    _durationSubscription?.cancel();
    _positionSubscription?.cancel();
    _audioPlayer.dispose();
    _waveAnimationController.dispose();
    _playButtonController.dispose();
    super.dispose();
  }

  Future<void> _togglePlayPause() async {
    if (!widget.isEnabled) return;

    try {
      if (_playerState == PlayerState.playing) {
        await _audioPlayer.pause();
      } else {
        if (_playerState == PlayerState.stopped ||
            _playerState == PlayerState.completed) {
          await _audioPlayer.play(UrlSource(widget.audioUrl));
        } else {
          await _audioPlayer.resume();
        }
      }
    } catch (e) {
      debugPrint('Error playing audio: $e');
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Play/Pause Button
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: GestureDetector(
              onTap: _togglePlayPause,
              child: AnimatedBuilder(
                animation: _playButtonController,
                builder: (context, child) {
                  return Container(
                    width: 44,
                    height: 44,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: const Color(0xffE21F29),
                      boxShadow: [
                        BoxShadow(
                          color: const Color(0xffE21F29).withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(
                      _playerState == PlayerState.playing
                          ? Icons.pause
                          : Icons.play_arrow,
                      color: Colors.white,
                      size: 24,
                    ),
                  );
                },
              ),
            ),
          ),

          // Sound Wave Visualization
          Expanded(
            child: Container(
              height: 40,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: AnimatedBuilder(
                animation: _waveAnimationController,
                builder: (context, child) {
                  return CustomPaint(
                    painter: SoundWavePainter(
                      animation: _waveAnimationController,
                      isPlaying: _playerState == PlayerState.playing,
                      progress:
                          _duration != null && _duration!.inMilliseconds > 0
                              ? _position.inMilliseconds /
                                  _duration!.inMilliseconds
                              : 0.0,
                    ),
                    size: Size.infinite,
                  );
                },
              ),
            ),
          ),

          // Time Display
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: Text(
              _formatDuration(_position),
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Color(0xffE21F29),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class SoundWavePainter extends CustomPainter {
  final Animation<double> animation;
  final bool isPlaying;
  final double progress;

  SoundWavePainter({
    required this.animation,
    required this.isPlaying,
    required this.progress,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final activePaint =
        Paint()
          ..color = const Color(0xffE21F29)
          ..style = PaintingStyle.fill;

    final inactivePaint =
        Paint()
          ..color = const Color(0xffE21F29).withValues(alpha: 0.3)
          ..style = PaintingStyle.fill;

    const int barCount = 40;
    final double barWidth = size.width / barCount;
    final double centerY = size.height / 2;

    for (int i = 0; i < barCount; i++) {
      final double x = i * barWidth + barWidth / 2;

      // Create varied bar heights for a more natural wave pattern
      final double baseHeight = _getBarHeight(i, barCount);
      final double animatedHeight =
          isPlaying
              ? baseHeight *
                  (0.4 +
                      0.6 *
                          (0.5 +
                              0.5 *
                                  math.sin(
                                    animation.value * 2 * math.pi + i * 0.4,
                                  )))
              : baseHeight * 0.3;

      final double barHeight = math.max(2.0, animatedHeight);

      // Determine if this bar should be active based on progress
      final bool isActive = (i / barCount) <= progress;

      final rect = RRect.fromRectAndRadius(
        Rect.fromCenter(
          center: Offset(x, centerY),
          width: barWidth * 0.6,
          height: barHeight,
        ),
        const Radius.circular(1),
      );

      canvas.drawRRect(rect, isActive ? activePaint : inactivePaint);
    }
  }

  double _getBarHeight(int index, int totalBars) {
    // Create a more realistic audio waveform pattern
    final double normalizedIndex = index / totalBars;

    // Create multiple frequency components for a more natural look
    final double wave1 = math.sin(normalizedIndex * math.pi * 3) * 0.4;
    final double wave2 = math.sin(normalizedIndex * math.pi * 7) * 0.25;
    final double wave3 = math.sin(normalizedIndex * math.pi * 13) * 0.15;
    final double wave4 = math.sin(normalizedIndex * math.pi * 19) * 0.1;

    // Add some randomness for more natural variation
    final double randomFactor =
        (index * 17) % 100 / 100.0; // Pseudo-random based on index
    final double randomVariation = (randomFactor - 0.5) * 0.2;

    // Combine waves with different amplitudes for realistic audio visualization
    final double combinedWave = wave1 + wave2 + wave3 + wave4 + randomVariation;

    // Ensure minimum height and scale appropriately
    return math.max(3.0, 12 + combinedWave * 15);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
